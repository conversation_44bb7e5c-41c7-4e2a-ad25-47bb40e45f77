'use client';

import React, { useState, useCallback, useEffect } from 'react';
import TextEditor from '@/components/common/TextEditor';

const LocationContactsManager = ({
  formData,
  errors,
  onQuillChange,
  onSectionSave,
  isLoading
}) => {
  const [isEditing, setIsEditing] = useState(false);
  const [showDeleteConfirm, setShowDeleteConfirm] = useState(false);
  const [isLoadingData, setIsLoadingData] = useState(false);
  const [dataError, setDataError] = useState(null);
  const [localFormData, setLocalFormData] = useState({
    title: '',
    body: '',
    details: ''
  });
  const [validationErrors, setValidationErrors] = useState({});

  // Update local form data when formData changes (following BookingDetailsText pattern)
  useEffect(() => {
    if (formData) {
      setLocalFormData({
        title: formData.title || '',
        body: formData.body || '',
        details: formData.details || ''
      });
    }
  }, [formData]);



  // Handle local form changes when in edit mode (following BookingDetailsText pattern)
  const handleLocalChange = useCallback((content, field) => {
    setLocalFormData(prev => ({
      ...prev,
      [field]: content
    }));
  }, []);

  // Fetch fresh data when entering edit mode (following BookingDetailsText pattern)
  const fetchLatestData = useCallback(async () => {
    setIsLoadingData(true);
    setDataError(null);

    try {
      const response = await fetch('/api/pages', {
        method: 'GET',
        headers: {
          'Content-Type': 'application/json',
        },
      });

      if (!response.ok) {
        throw new Error(`HTTP error! status: ${response.status}`);
      }

      const data = await response.json();

      if (data.success) {
        const locationData = data.data?.locationAndcontacts || {};
        const freshFormData = {
          title: locationData.title || '',
          body: locationData.body || '',
          details: locationData.details || ''
        };
        setLocalFormData(freshFormData);
        console.log('✅ Successfully fetched latest location data');
      } else {
        throw new Error(data.message || 'Failed to fetch latest data');
      }
    } catch (error) {
      console.error('❌ Error fetching latest location data:', error);
      setDataError(`Failed to load latest data: ${error.message}. Using current values.`);
      // Fallback to current form data
      const currentFormData = {
        title: formData?.title || '',
        body: formData?.body || '',
        details: formData?.details || ''
      };
      setLocalFormData(currentFormData);
    } finally {
      setIsLoadingData(false);
    }
  }, [formData]);

  // Handle edit mode toggle (following BookingDetailsText pattern)
  const handleEdit = useCallback(async () => {
    setIsEditing(true);
    await fetchLatestData();
  }, [fetchLatestData]);

  // Handle save changes (following BookingDetailsText pattern)
  const handleSave = useCallback(async () => {
    try {
      // Validate required fields
      if (!localFormData.title?.trim() || !localFormData.body?.trim() || !localFormData.details?.trim()) {
        alert('Please fill in all required fields: Title, Body Content, and Details');
        return;
      }

      // Update the parent form data first (synchronously)
      Object.keys(localFormData).forEach(field => {
        onQuillChange(localFormData[field], 'locationAndcontacts', field);
      });

      // Wait a brief moment for state updates to propagate
      await new Promise(resolve => setTimeout(resolve, 100));

      // Save to API with the current local form data
      await onSectionSave('locationAndcontacts', localFormData);
      setIsEditing(false);
    } catch (error) {
      console.error('Error saving location & contacts:', error);
      alert('Failed to save changes. Please try again.');
      // Don't exit edit mode if save failed
    }
  }, [localFormData, onQuillChange, onSectionSave]);

  // Handle cancel edit (following BookingDetailsText pattern)
  const handleCancel = useCallback(() => {
    // Reset to current form data when canceling
    setLocalFormData({
      title: formData?.title || '',
      body: formData?.body || '',
      details: formData?.details || ''
    });
    setIsEditing(false);
  }, [formData]);

  // Handle delete (following BookingDetailsText pattern)
  const handleDelete = useCallback(async () => {
    try {
      // Clear all fields
      const emptyData = {
        title: '',
        body: '',
        details: ''
      };

      Object.keys(emptyData).forEach(field => {
        onQuillChange(emptyData[field], 'locationAndcontacts', field);
      });

      await onSectionSave();
      setShowDeleteConfirm(false);
      setIsEditing(false);
    } catch (error) {
      console.error('Error deleting location & contacts:', error);
    }
  }, [onQuillChange, onSectionSave]);

  const currentData = isEditing ? localFormData : formData;
  const hasContent = formData.title || formData.body || formData.details;

  return (
    <div className="space-y-6">
      <div className="flex items-center justify-between">
        <h3 className="text-lg font-medium text-gray-900">Location & Contacts Management</h3>
        <div className="flex space-x-2">
          {!isEditing ? (
            <>
              <button
                type="button"
                onClick={handleEdit}
                disabled={isLoadingData}
                className="px-4 py-2 text-sm font-medium text-blue-600 bg-blue-50 border border-blue-200 rounded-md hover:bg-blue-100 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-blue-500 disabled:opacity-50 disabled:cursor-not-allowed"
              >
                {isLoadingData ? 'Loading...' : 'Edit'}
              </button>
              {hasContent && (
                <button
                  type="button"
                  onClick={() => setShowDeleteConfirm(true)}
                  className="px-4 py-2 text-sm font-medium text-red-600 bg-red-50 border border-red-200 rounded-md hover:bg-red-100 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-red-500"
                >
                  Delete
                </button>
              )}
              <button
                type="button"
                onClick={onSectionSave}
                disabled={isLoading}
                className="px-4 py-2 text-sm font-medium text-white bg-green-600 border border-transparent rounded-md hover:bg-green-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-green-500 disabled:opacity-50 disabled:cursor-not-allowed"
              >
                {isLoading ? 'Saving...' : 'Save Section'}
              </button>
            </>
          ) : (
            <>
              <button
                type="button"
                onClick={handleCancel}
                className="px-4 py-2 text-sm font-medium text-gray-700 bg-white border border-gray-300 rounded-md hover:bg-gray-50 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-gray-500"
              >
                Cancel
              </button>
              <button
                type="button"
                onClick={handleSave}
                disabled={isLoading}
                className="px-4 py-2 text-sm font-medium text-white bg-blue-600 border border-transparent rounded-md hover:bg-blue-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-blue-500 disabled:opacity-50 disabled:cursor-not-allowed"
              >
                {isLoading ? 'Saving...' : 'Save Changes'}
              </button>
            </>
          )}
        </div>
      </div>

      {/* Delete Confirmation Modal */}
      {showDeleteConfirm && (
        <div className="fixed inset-0 bg-gray-600 bg-opacity-50 overflow-y-auto h-full w-full z-50">
          <div className="relative top-20 mx-auto p-5 border w-96 shadow-lg rounded-md bg-white">
            <div className="mt-3 text-center">
              <h3 className="text-lg font-medium text-gray-900">Confirm Delete</h3>
              <div className="mt-2 px-7 py-3">
                <p className="text-sm text-gray-500">
                  Are you sure you want to delete all Location & Contacts content? This action cannot be undone.
                </p>
              </div>
              <div className="flex justify-center space-x-3 mt-4">
                <button
                  onClick={() => setShowDeleteConfirm(false)}
                  className="px-4 py-2 text-sm font-medium text-gray-700 bg-white border border-gray-300 rounded-md hover:bg-gray-50"
                >
                  Cancel
                </button>
                <button
                  onClick={handleDelete}
                  className="px-4 py-2 text-sm font-medium text-white bg-red-600 border border-transparent rounded-md hover:bg-red-700"
                >
                  Delete
                </button>
              </div>
            </div>
          </div>
        </div>
      )}

      {/* Data Error Display */}
      {dataError && (
        <div className="bg-yellow-50 border border-yellow-200 rounded-md p-4">
          <div className="flex">
            <div className="flex-shrink-0">
              <svg className="h-5 w-5 text-yellow-400" viewBox="0 0 20 20" fill="currentColor">
                <path fillRule="evenodd" d="M8.257 3.099c.765-1.36 2.722-1.36 3.486 0l5.58 9.92c.75 1.334-.213 2.98-1.742 2.98H4.42c-1.53 0-2.493-1.646-1.743-2.98l5.58-9.92zM11 13a1 1 0 11-2 0 1 1 0 012 0zm-1-8a1 1 0 00-1 1v3a1 1 0 002 0V6a1 1 0 00-1-1z" clipRule="evenodd" />
              </svg>
            </div>
            <div className="ml-3">
              <p className="text-sm text-yellow-800">{dataError}</p>
            </div>
          </div>
        </div>
      )}

      {/* Loading State */}
      {isLoadingData && (
        <div className="bg-blue-50 border border-blue-200 rounded-md p-4">
          <div className="flex items-center">
            <div className="animate-spin rounded-full h-4 w-4 border-b-2 border-blue-600 mr-3"></div>
            <p className="text-sm text-blue-800">Loading latest data...</p>
          </div>
        </div>
      )}

      {/* Content Display/Edit Form */}
      {!hasContent && !isEditing ? (
        <div className="text-center py-12 bg-gray-50 rounded-lg border-2 border-dashed border-gray-300">
          <p className="text-gray-500 mb-4">No location & contacts content available</p>
          <button
            onClick={handleEdit}
            disabled={isLoadingData}
            className="px-4 py-2 text-sm font-medium text-blue-600 bg-blue-50 border border-blue-200 rounded-md hover:bg-blue-100 disabled:opacity-50 disabled:cursor-not-allowed"
          >
            {isLoadingData ? 'Loading...' : 'Create Content'}
          </button>
        </div>
      ) : (
        <div className="space-y-6">
          {/* Title */}
          <div>
            <label className="block text-sm font-medium text-gray-700 mb-2">
              Title *
            </label>
            {isEditing ? (
              <div className={`${errors['locationAndcontacts.title'] ? 'border-red-500' : ''}`}>
                <TextEditor
                  key={`title-edit-${isEditing}`}
                  value={currentData.title}
                  onChange={(content) => handleLocalChange(content, 'title')}
                  placeholder="Enter location page title"
                  style={{ minHeight: '80px' }}
                  className={`border rounded-md ${errors['locationAndcontacts.title'] ? 'border-red-500' : 'border-gray-300'}`}
                />
              </div>
            ) : (
              <div className="p-4 bg-gray-50 rounded-md border">
                <div dangerouslySetInnerHTML={{ __html: currentData.title || 'No title set' }} />
              </div>
            )}
            {errors['locationAndcontacts.title'] && (
              <p className="mt-1 text-sm text-red-600">{errors['locationAndcontacts.title']}</p>
            )}
          </div>

          {/* Body Content */}
          <div>
            <label className="block text-sm font-medium text-gray-700 mb-2">
              Body Content *
            </label>
            {isEditing ? (
              <div className={`${errors['locationAndcontacts.body'] ? 'border-red-500' : ''}`}>
                <TextEditor
                  key={`body-edit-${isEditing}`}
                  value={currentData.body}
                  onChange={(content) => handleLocalChange(content, 'body')}
                  placeholder="Enter location body content"
                  style={{ minHeight: '120px' }}
                  className={`border rounded-md ${errors['locationAndcontacts.body'] ? 'border-red-500' : 'border-gray-300'}`}
                />
              </div>
            ) : (
              <div className="p-4 bg-gray-50 rounded-md border">
                <div dangerouslySetInnerHTML={{ __html: currentData.body || 'No body content set' }} />
              </div>
            )}
            {errors['locationAndcontacts.body'] && (
              <p className="mt-1 text-sm text-red-600">{errors['locationAndcontacts.body']}</p>
            )}
          </div>

          {/* Details */}
          <div>
            <label className="block text-sm font-medium text-gray-700 mb-2">
              Details *
            </label>
            {isEditing ? (
              <div className={`${errors['locationAndcontacts.details'] ? 'border-red-500' : ''}`}>
                <TextEditor
                  key={`details-edit-${isEditing}`}
                  value={currentData.details}
                  onChange={(content) => handleLocalChange(content, 'details')}
                  placeholder="Enter location details"
                  style={{ minHeight: '120px' }}
                  className={`border rounded-md ${errors['locationAndcontacts.details'] ? 'border-red-500' : 'border-gray-300'}`}
                />
              </div>
            ) : (
              <div className="p-4 bg-gray-50 rounded-md border">
                <div dangerouslySetInnerHTML={{ __html: currentData.details || 'No details set' }} />
              </div>
            )}
            {errors['locationAndcontacts.details'] && (
              <p className="mt-1 text-sm text-red-600">{errors['locationAndcontacts.details']}</p>
            )}
          </div>


        </div>
      )}
    </div>
  );
};

export default LocationContactsManager;
